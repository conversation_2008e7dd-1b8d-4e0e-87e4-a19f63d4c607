import React, { useState, useEffect } from 'react';
import {
    Search,
    Star,
    Calendar,
    Trophy,
    Crown
} from 'lucide-react';
import { apiClient } from '../api';
import { generateAvatarUrl } from '../utils/avatar';
import type { ProjectWithCreator } from '../types';

interface ProjectPlazaProps {
    onUserClick?: (userId: number, username: string) => void;
}

const ProjectPlaza: React.FC<ProjectPlazaProps> = ({ onUserClick }) => {
    const [projects, setProjects] = useState<ProjectWithCreator[]>([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');

    useEffect(() => {
        loadProjects();
    }, []);

    const loadProjects = async () => {
        try {
            setLoading(true);
            const data = await apiClient.getPlazaProjects();
            setProjects(data);
        } catch (error) {
            console.error('Failed to load plaza projects:', error);
        } finally {
            setLoading(false);
        }
    };

    const filteredProjects = projects.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        project.creator_username.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const featuredProjects = filteredProjects.filter(p => p.is_featured);
    const regularProjects = filteredProjects.filter(p => !p.is_featured);

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('zh-CN');
    };



    const ProjectCard: React.FC<{ project: ProjectWithCreator; isFeatured?: boolean }> = ({ 
        project, 
        isFeatured = false 
    }) => (
        <div className={`rounded-xl shadow-sm border overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105 ${
            isFeatured 
                ? 'border-yellow-300 bg-gradient-to-br from-yellow-50 to-orange-50' 
                : 'border-gray-200 bg-white'
        }`}>
            {/* 官方精选标识 */}
            {isFeatured && (
                <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-4 py-2 text-sm font-medium flex items-center">
                    <Crown className="h-4 w-4 mr-2" />
                    官方精选
                </div>
            )}
            
            <div className="p-6">
                {/* 项目标题 */}
                <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                        {project.name}
                    </h3>
                </div>

                {/* 项目描述 */}
                <div className="mb-4">
                    {project.description ? (
                        <p className="text-gray-600 text-sm line-clamp-3">
                            {project.description}
                        </p>
                    ) : (
                        <p className="text-gray-400 text-sm italic">
                            暂无项目描述
                        </p>
                    )}
                </div>

                {/* 创建者信息 */}
                <div className="flex items-center justify-between">
                    <button
                        onClick={() => onUserClick?.(project.creator_user_id, project.creator_username)}
                        className="flex items-center space-x-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
                    >
                        {project.creator_avatar_url ? (
                            <img
                                src={project.creator_avatar_url}
                                alt={project.creator_username}
                                className="h-6 w-6 rounded-full"
                            />
                        ) : (
                            <img
                                src={generateAvatarUrl(project.creator_username)}
                                alt={project.creator_username}
                                className="h-6 w-6 rounded-full"
                            />
                        )}
                        <span className="font-medium">{project.creator_username}</span>
                    </button>
                    
                    <div className="flex items-center text-xs text-gray-400">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(project.created_at)}
                    </div>
                </div>
            </div>
        </div>
    );

    return (
        <div className="p-6">
            <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">项目广场</h1>
                <p className="text-gray-600">发现优秀的项目和配置</p>
            </div>

            {/* 搜索框 */}
            <div className="mb-6">
                <div className="relative max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                        type="text"
                        placeholder="搜索项目或用户..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
            </div>

            {loading ? (
                <div className="text-center py-12">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <p className="mt-2 text-gray-600">加载中...</p>
                </div>
            ) : (
                <div className="space-y-8">
                    {/* 官方精选项目 */}
                    {featuredProjects.length > 0 && (
                        <div>
                            <div className="flex items-center mb-4">
                                <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                                <h2 className="text-xl font-semibold text-gray-900">官方精选</h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {featuredProjects.map((project) => (
                                    <ProjectCard 
                                        key={project.id} 
                                        project={project} 
                                        isFeatured={true}
                                    />
                                ))}
                            </div>
                        </div>
                    )}

                    {/* 精选项目 */}
                    {regularProjects.length > 0 && (
                        <div>
                            <div className="flex items-center mb-4">
                                <Star className="h-5 w-5 text-blue-500 mr-2" />
                                <h2 className="text-xl font-semibold text-gray-900">精选项目</h2>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {regularProjects.map((project) => (
                                    <ProjectCard 
                                        key={project.id} 
                                        project={project}
                                    />
                                ))}
                            </div>
                        </div>
                    )}

                    {/* 空状态 */}
                    {filteredProjects.length === 0 && !loading && (
                        <div className="text-center py-12">
                            <Trophy className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无精选项目</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                {searchTerm ? '没有找到匹配的项目' : '还没有精选项目'}
                            </p>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default ProjectPlaza;
