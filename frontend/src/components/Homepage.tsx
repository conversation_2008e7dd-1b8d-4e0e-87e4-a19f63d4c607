import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import './Homepage.css';

const Homepage: React.FC = () => {
  const [currentSlide, setCurrentSlide] = React.useState(0);
  const screenshots = [
    { src: '/ui.png', alt: '任务列表界面', description: '任务列表界面 - 直观管理所有任务' },
    { src: '/ui-add.png', alt: '任务添加界面', description: '任务添加界面 - 脚本任务可定时执行' }
  ];

  useEffect(() => {
    // 导航栏滚动效果
    const header = document.querySelector('header');
    const backToTopBtn = document.getElementById('back-to-top');

    const handleScroll = () => {
      if (window.scrollY > 50) {
        header?.classList.add('scrolled');
        backToTopBtn?.classList.add('visible');
      } else {
        header?.classList.remove('scrolled');
        backToTopBtn?.classList.remove('visible');
      }
    };

    window.addEventListener('scroll', handleScroll);

    // 返回顶部按钮
    const handleBackToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    };

    backToTopBtn?.addEventListener('click', handleBackToTop);

    // 移动端菜单切换
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    const handleMenuToggle = () => {
      navLinks?.classList.toggle('active');
    };

    menuToggle?.addEventListener('click', handleMenuToggle);

    // 清理事件监听器
    return () => {
      window.removeEventListener('scroll', handleScroll);
      backToTopBtn?.removeEventListener('click', handleBackToTop);
      menuToggle?.removeEventListener('click', handleMenuToggle);
    };
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % screenshots.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + screenshots.length) % screenshots.length);
  };

  return (
    <div className="homepage">
      {/* 导航栏 */}
      <header>
        <nav className="container">
          <div className="logo">
            <img src="/logo.png" alt="P-Box Logo" />
            <span>P-Box</span>
          </div>
          <div className="nav-links">
            <a href="#features">功能特点</a>
            <a href="#screenshots">界面展示</a>
            <a href="#download">立即下载</a>
            <a href="#contact">联系我们</a>
            <Link to="/login" className="nav-admin-link">管理后台</Link>
          </div>
          <div className="menu-toggle">
            <i className="fas fa-bars"></i>
          </div>
        </nav>
      </header>

      {/* 英雄区域 */}
      <section className="hero">
        <div className="container">
          <div className="hero-content" data-aos="fade-right">
            <h1>P-Box 项目任务管理器</h1>
            <p>高效管理您的项目任务，提升工作效率</p>
            <div className="hero-buttons">
              <a href="#download" className="btn primary-btn">立即下载</a>
              <a href="#features" className="btn secondary-btn">了解更多</a>
            </div>
          </div>
          <div className="hero-image" data-aos="fade-left">
            <img src="/ui.png" alt="P-Box 应用截图" />
          </div>
        </div>
      </section>

      {/* 功能特点 */}
      <section id="features" className="features">
        <div className="container">
          <h2 className="section-title" data-aos="fade-up">功能特点</h2>
          <div className="features-grid">
            <div className="feature-card" data-aos="fade-up" data-aos-delay="100">
              <div className="feature-icon">
                <i className="fas fa-tasks"></i>
              </div>
              <h3>任务管理</h3>
              <p>轻松创建、编辑和管理项目任务，支持分类和搜索功能</p>
            </div>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="200">
              <div className="feature-icon">
                <i className="fas fa-clock"></i>
              </div>
              <h3>定时执行</h3>
              <p>设置任务的执行周期，支持按天、小时、分钟定期执行</p>
            </div>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="300">
              <div className="feature-icon">
                <i className="fas fa-terminal"></i>
              </div>
              <h3>脚本执行</h3>
              <p>支持执行自定义脚本，可设置终止指令，灵活控制任务执行</p>
            </div>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="400">
              <div className="feature-icon">
                <i className="fas fa-file-export"></i>
              </div>
              <h3>导入导出</h3>
              <p>支持任务配置的导入导出，方便在不同设备间迁移</p>
            </div>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="500">
              <div className="feature-icon">
                <i className="fas fa-grip-horizontal"></i>
              </div>
              <h3>项目广场</h3>
              <p>将推出项目广场，分享好玩的项目</p>
            </div>
            <div className="feature-card" data-aos="fade-up" data-aos-delay="600">
              <div className="feature-icon">
                <i className="fas fa-desktop"></i>
              </div>
              <h3>跨平台支持</h3>
              <p>支持Windows和macOS系统</p>
            </div>
          </div>
        </div>
      </section>

      {/* 界面展示 */}
      <section id="screenshots" className="screenshots">
        <div className="container">
          <h2 className="section-title" data-aos="fade-up">界面展示</h2>
          <div className="screenshot-container" data-aos="fade-up">
            <button className="slider-btn prev-btn" onClick={prevSlide}>
              <i className="fas fa-chevron-left"></i>
            </button>
            <div className="screenshot-slider">
              {screenshots.map((screenshot, index) => (
                <div
                  key={index}
                  className={`screenshot ${index === currentSlide ? 'active' : ''}`}
                >
                  <img src={screenshot.src} alt={screenshot.alt} />
                  <p>{screenshot.description}</p>
                </div>
              ))}
            </div>
            <button className="slider-btn next-btn" onClick={nextSlide}>
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </section>

      {/* 下载区域 */}
      <section id="download" className="download">
        <div className="container">
          <h2 className="section-title" data-aos="fade-up">立即下载</h2>
          <div className="download-options" data-aos="fade-up">
            <a href="https://newonet.com/media/updates/p-box 安装包-Windows版.zip" target="_blank" rel="noopener noreferrer" className="download-btn">
              <i className="fab fa-windows"></i>
              <span>Windows</span>
            </a>
            <a href="https://newonet.com/media/updates/p-box 安装包-Mac版.zip" target="_blank" rel="noopener noreferrer" className="download-btn">
              <i className="fab fa-apple"></i>
              <span>MacOS</span>
            </a>
          </div>
        </div>
      </section>

      {/* 联系我们 */}
      <section id="contact" className="contact">
        <div className="container">
          <h2 className="section-title" data-aos="fade-up">联系我们</h2>
          <div className="contact-content" data-aos="fade-up">
            <div className="contact-info">
              <div className="contact-item">
                <i className="fab fa-weixin"></i>
                <p>微信: mwddkf001</p>
              </div>
              <p className="contact-description">
                如有任何问题或建议，欢迎通过微信联系我们。我们将尽快回复您的咨询。
              </p>
            </div>
            <div className="qr-code">
              <div className="qr-placeholder">
                <img src="/wechat.png" alt="wechat" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer>
        <div className="container">
          <div className="footer-content">
            <div className="footer-logo">
              <img src="/logo.png" alt="P-Box Logo" />
              <span>P-Box</span>
            </div>
            <a className="copyright" href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer">皖ICP备18009967号-12</a>
            <p className="copyright">© 2025 newonet.com 保留所有权利。</p>
          </div>
        </div>
      </footer>

      {/* 返回顶部按钮 */}
      <button id="back-to-top" className="back-to-top">
        <i className="fas fa-arrow-up"></i>
      </button>
    </div>
  );
};

export default Homepage;
