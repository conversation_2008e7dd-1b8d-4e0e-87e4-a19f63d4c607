
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import PublicLayout from './components/PublicLayout';
import Homepage from './components/Homepage';
import Dashboard from './components/Dashboard';
import Platforms from './components/Platforms';
import Architectures from './components/Architectures';
import Releases from './components/Releases';
import UpdateChecks from './components/UpdateChecks';
import DownloadRecords from './components/DownloadRecords';
import Profile from './components/Profile';
import Projects from './components/Projects';
import AdminProjects from './components/AdminProjects';
import ProjectPlaza from './components/ProjectPlaza';
import UserProjects from './components/UserProjects';
import Login from './components/Login';
import Register from './components/Register';
import ProtectedRoute from './components/ProtectedRoute';
import { useState } from 'react';

function App() {
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedUsername, setSelectedUsername] = useState<string>('');
  const [showUserProjects, setShowUserProjects] = useState(false);

  const handleUserClick = (userId: number, username: string) => {
    setSelectedUserId(userId);
    setSelectedUsername(username);
    setShowUserProjects(true);
  };

  const handleBackToPlaza = () => {
    setShowUserProjects(false);
    setSelectedUserId(null);
    setSelectedUsername('');
  };

  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* 公开路由 - 首页 */}
          <Route path="/" element={<Homepage />} />

          {/* 公开路由 - 认证 */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* 公开路由 - 项目广场 */}
          <Route path="/plaza" element={
            <PublicLayout>
              {showUserProjects && selectedUserId ? (
                <UserProjects
                  userId={selectedUserId}
                  username={selectedUsername}
                  onBack={handleBackToPlaza}
                />
              ) : (
                <ProjectPlaza onUserClick={handleUserClick} />
              )}
            </PublicLayout>
          } />

          {/* 受保护的路由 - 管理后台 */}
          <Route path="/admin/*" element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  {/* 管理后台首页指向仪表板 */}
                  <Route path="/" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Dashboard />
                    </ProtectedRoute>
                  } />

                  {/* 需要管理员权限的路由 */}
                  <Route path="/dashboard" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Dashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/platforms" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Platforms />
                    </ProtectedRoute>
                  } />
                  <Route path="/architectures" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Architectures />
                    </ProtectedRoute>
                  } />
                  <Route path="/releases" element={
                    <ProtectedRoute requireAdmin={true}>
                      <Releases />
                    </ProtectedRoute>
                  } />
                  <Route path="/update-checks" element={
                    <ProtectedRoute requireAdmin={true}>
                      <UpdateChecks />
                    </ProtectedRoute>
                  } />
                  <Route path="/download-records" element={
                    <ProtectedRoute requireAdmin={true}>
                      <DownloadRecords />
                    </ProtectedRoute>
                  } />
                  <Route path="/projects-admin" element={
                    <ProtectedRoute requireAdmin={true}>
                      <AdminProjects />
                    </ProtectedRoute>
                  } />

                  {/* 普通用户也可以访问的路由 */}
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/projects" element={<Projects />} />

                  {/* 项目广场路由 */}
                  <Route path="/plaza" element={
                    showUserProjects && selectedUserId ? (
                      <UserProjects
                        userId={selectedUserId}
                        username={selectedUsername}
                        onBack={handleBackToPlaza}
                      />
                    ) : (
                      <ProjectPlaza onUserClick={handleUserClick} />
                    )
                  } />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
